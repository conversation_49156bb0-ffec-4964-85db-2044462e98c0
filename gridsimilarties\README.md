# Activation Map System

A sophisticated 4x6 grid activation map system that visualizes algorithmic similarity detection between base images and activation targets, with support for both static image and video modes.

## Features

- **4x6 Grid Layout**: 24 individual cells, each with its own base image and activation pattern
- **Dual Mode Support**: 
  - Picture Mode: Static images with activation overlays
  - Video Mode: Same video in all cells with different activation patterns per cell
- **3-Color Gradient System**: Customizable color picker for activation visualization
- **Algorithmic Similarity Detection**: Advanced pixel-level similarity analysis with spatial tolerance
- **Real-time Controls**: Adjustable sensitivity and spatial tolerance settings
- **Responsive Design**: Adapts to different screen sizes

## File Structure

```
gridsimilarties/
├── index.html                 # Main application interface
├── css/
│   └── styles.css            # Complete styling for the application
├── js/
│   ├── main.js               # Main application controller
│   ├── gridManager.js        # Grid creation and management
│   ├── colorController.js    # 3-color gradient system
│   ├── similarityDetector.js # Algorithmic similarity detection
│   └── activationRenderer.js # Activation visualization rendering
├── config/
│   └── settings.json         # Application configuration
├── assets/
│   ├── images/
│   │   ├── base/            # Base images (baseimage1.jpg, baseimage2.jpg, etc.)
│   │   └── activation/      # Activation target images (activationimage1.jpg, etc.)
│   └── videos/
│       └── main.mp4         # Main video file for video mode
└── README.md                # This file
```

## Setup Instructions

### 1. Prepare Your Assets

#### Base Images
Place your base images in `assets/images/base/` with the naming convention:
- `baseimage1.jpg` or `baseimage1.png`
- `baseimage2.jpg` or `baseimage2.png`
- ... up to `baseimage24.jpg` or `baseimage24.png`

#### Activation Images
**Option 1: Single Activation Image (Recommended)**
Place a single activation image in `assets/images/activation/` as:
- `activation.jpg` or `activation.png`

This single image will be used across all cells, creating varied responses based on each unique base image.

**Option 2: Individual Activation Images**
Place individual activation images in `assets/images/activation/` with the naming convention:
- `activation1.jpg` or `activation1.png`
- `activation2.jpg` or `activation2.png`
- ... up to `activation24.jpg` or `activation24.png`

To switch between modes, edit `config/settings.json` and set `"useSingleActivationImage": true` or `false`.

#### Video (Optional)
Place your main video file in `assets/videos/` as:
- `main.mp4`, `main.webm`, or `main.ogg`

### 2. Launch the Application

1. Open VS Code in the project directory
2. Install the Live Server extension if not already installed
3. Right-click on `index.html` and select "Open with Live Server"
4. The application will open in your default browser

## How It Works

### Enhanced Similarity Detection Algorithm

The system uses an advanced algorithmic approach that creates organic, varied responses across different base images:

#### Core Features:
1. **Adaptive Spatial Tolerance**: Checks pixels within a configurable radius (1-10 pixels)
2. **Dual Color Space Support**: Choose between RGB or HSV color comparison for different perceptual results
3. **Sensitivity Curves**: Applies power curves to enhance or reduce activation intensity
4. **Single Activation Image**: Uses one activation image across all cells, creating unique patterns based on each base image's content

#### Advanced Enhancements:
5. **Contrast Adjustment**: Dynamically adjusts similarity contrast for more dramatic or subtle effects
6. **Edge Detection Boost**: Automatically detects edges using Sobel operators and boosts activations near high-frequency areas
7. **Color Rarity Weighting**: Analyzes color distribution and boosts activations for rare colors in each base image
8. **Adaptive Thresholding**: Filters out weak activations below a configurable threshold
9. **Organic Variation**: Each base image produces unique activation patterns even with the same activation image

#### Why It Creates Varied Responses:
- **Content-Driven Adaptation**: Different base images have unique color distributions, edge patterns, and content
- **Contextual Analysis**: The algorithm analyzes each base image's characteristics and adapts accordingly
- **Multi-Factor Processing**: Combines color similarity, edge detection, rarity analysis, and spatial tolerance
- **Non-Linear Enhancement**: Multiple enhancement layers create complex, organic activation patterns

### Controls

#### Mode Selection
- **Picture Mode**: Shows static images with activation overlays
- **Video Mode**: Plays the same video in all cells with different activation patterns

#### Color Controls
- **Low Activation**: Color for areas with low similarity (default: blue)
- **Medium Activation**: Color for areas with medium similarity (default: yellow)
- **High Activation**: Color for areas with high similarity (default: red)
- **Apply Colors**: Reprocesses all activations with new colors

#### Enhanced Similarity Settings
- **Spatial Tolerance**: How many pixels around each point to check (1-10)
- **Sensitivity**: How sensitive the detection is (0.1-1.0)
- **Contrast**: Adjusts the contrast of similarity detection (0.1-3.0)
- **Edge Boost**: Enhances activations near edges and high-frequency areas (0.0-2.0)
- **Color Rarity**: Boosts activations for rare colors in the base image (0.0-1.0)
- **Threshold**: Minimum similarity value to display (0.0-1.0)
- **Color Space**: Choose between RGB or HSV color comparison

## Technical Details

### Image Processing
- Images are automatically resized to maintain aspect ratio
- Maximum processing size is 512x512 pixels for performance
- Supports both JPG and PNG formats
- Creates placeholder images if assets are missing

### Performance Optimization
- Debounced reprocessing when adjusting settings
- Efficient canvas-based rendering
- Spatial tolerance limits search radius for performance

### Browser Compatibility
- Modern browsers with HTML5 Canvas support
- Chrome, Firefox, Safari, Edge (latest versions)
- Requires JavaScript enabled

## Customization

### Configuration
Edit `config/settings.json` to modify:
- Grid dimensions
- File paths
- Default colors
- Similarity parameters
- Video settings

### Styling
Modify `css/styles.css` to customize:
- Grid layout and spacing
- Color schemes
- Animation effects
- Responsive breakpoints

## Troubleshooting

### Common Issues

1. **Images not loading**: Check file paths and naming conventions
2. **Video not playing**: Ensure video format is supported (MP4 recommended)
3. **Slow performance**: Reduce image sizes or adjust spatial tolerance
4. **CORS errors**: Use Live Server extension, don't open HTML file directly

### Browser Console
Check the browser console (F12) for detailed error messages and debugging information.

## Future Enhancements

Potential improvements could include:
- HSV color space similarity detection
- Gaussian blur post-processing
- Contour-based visualization
- Export functionality for activation maps
- Batch processing capabilities
- Custom similarity algorithms

## License

This project is provided as-is for educational and research purposes.
