/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
    background-color: #000000;
    color: #ffffff;
    overflow-x: auto;
}

.container {
    max-width: 1800px;
    margin: 0 auto;
    padding: 20px;
}

/* Control Panel Styles */
.control-panel {
    background: linear-gradient(135deg, #000000, #1a1a1a);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 100, 200, 0.3);
    border: 1px solid #0066cc;
}

.control-panel h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #ffffff;
    font-size: 2.2em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.control-panel h3 {
    margin-bottom: 15px;
    color: #66b3ff;
    font-size: 1.1em;
    font-weight: 600;
}

/* Mode Controls */
.mode-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
}

.mode-btn {
    padding: 12px 30px;
    border: 2px solid #0066cc;
    border-radius: 25px;
    background: linear-gradient(135deg, #000000, #1a1a1a);
    color: #66b3ff;
    font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 100, 200, 0.2);
}

.mode-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 100, 200, 0.4);
    border-color: #0080ff;
    color: #80ccff;
}

.mode-btn.active {
    background: linear-gradient(135deg, #0066cc, #0080ff);
    color: #ffffff;
    border-color: #0080ff;
    box-shadow: 0 4px 15px rgba(0, 128, 255, 0.5);
}

.fullscreen-hint {
    margin-top: 15px;
    text-align: center;
    color: #66b3ff;
    font-size: 0.9em;
    font-weight: 500;
    padding: 8px 16px;
    background: rgba(0, 102, 204, 0.1);
    border: 1px solid #0066cc;
    border-radius: 20px;
    display: inline-block;
}

.fullscreen-hint strong {
    color: #80ccff;
    font-weight: 700;
    background: rgba(0, 128, 255, 0.2);
    padding: 2px 6px;
    border-radius: 4px;
    margin: 0 2px;
}

/* Color Controls */
.color-controls {
    margin-bottom: 25px;
}

.color-picker-group {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.color-input {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.color-input label {
    font-size: 0.9em;
    color: #66b3ff;
    font-weight: 500;
}

.color-input input[type="color"] {
    width: 60px;
    height: 40px;
    border: 2px solid #0066cc;
    border-radius: 8px;
    cursor: pointer;
    background: #000000;
    box-shadow: 0 2px 8px rgba(0, 100, 200, 0.3);
}

#applyColors {
    display: block;
    margin: 0 auto;
    padding: 10px 25px;
    background: linear-gradient(135deg, #0066cc, #0080ff);
    color: white;
    border: 2px solid #0080ff;
    border-radius: 20px;
    font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 128, 255, 0.3);
}

#applyColors:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 128, 255, 0.5);
    background: linear-gradient(135deg, #0080ff, #00a0ff);
}

/* Similarity Controls */
.similarity-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
}

.slider-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.slider-group label {
    font-size: 0.9em;
    color: #66b3ff;
    font-weight: 500;
}

.slider-group input[type="range"] {
    width: 150px;
    height: 6px;
    border-radius: 3px;
    background: #1a1a1a;
    outline: none;
    cursor: pointer;
    border: 1px solid #0066cc;
}

.slider-group input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: linear-gradient(135deg, #0066cc, #0080ff);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 128, 255, 0.4);
    border: 2px solid #ffffff;
}

.slider-group span {
    font-weight: 600;
    color: #ffffff;
    background: linear-gradient(135deg, #0066cc, #0080ff);
    padding: 4px 12px;
    border-radius: 12px;
    min-width: 30px;
    text-align: center;
    border: 1px solid #0080ff;
}

.slider-group select {
    width: 150px;
    padding: 8px;
    background: #1a1a1a;
    color: #66b3ff;
    border: 1px solid #0066cc;
    border-radius: 4px;
    font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
    cursor: pointer;
}

.slider-group select:focus {
    outline: none;
    border-color: #0080ff;
    box-shadow: 0 0 5px rgba(0, 128, 255, 0.3);
}

/* Apply Controls */
.apply-controls {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.apply-btn {
    padding: 12px 30px;
    background: linear-gradient(135deg, #0066cc, #0080ff);
    color: white;
    border: 2px solid #0080ff;
    border-radius: 25px;
    font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
    font-weight: 600;
    font-size: 1.1em;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 128, 255, 0.3);
}

.apply-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 128, 255, 0.5);
    background: linear-gradient(135deg, #0080ff, #00a0ff);
}

.apply-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(0, 128, 255, 0.4);
}

/* Grid Container */
.grid-container {
    background: linear-gradient(135deg, #000000, #1a1a1a);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 100, 200, 0.3);
    border: 1px solid #0066cc;
    position: relative;
}

.activation-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 15px;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
}

/* Grid Cell Styles */
.grid-cell {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    background: #000000;
    border: 2px solid #0066cc;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 100, 200, 0.2);
}

.grid-cell:hover {
    border-color: #0080ff;
    box-shadow: 0 6px 20px rgba(0, 128, 255, 0.4);
    transform: scale(1.02);
    cursor: pointer;
}

.grid-cell img,
.grid-cell video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.grid-cell video {
    display: none;
}

.grid-cell.video-mode img {
    display: none;
}

.grid-cell.video-mode video {
    display: block;
}

/* Activation Overlay */
.activation-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0.7;
    mix-blend-mode: multiply;
    transition: opacity 0.3s ease;
    object-fit: cover;
}

.grid-cell:hover .activation-overlay {
    opacity: 0.8;
}

/* Cell Labels */
.cell-label {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(0, 0, 0, 0.8);
    color: #66b3ff;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7em;
    font-weight: 600;
    z-index: 10;
    border: 1px solid #0066cc;
    transition: opacity 0.3s ease;
}

/* Fullscreen Styles */
.fullscreen-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #000000;
    z-index: 9999;
    display: none;
    padding: 0;
}

.fullscreen-container.active {
    display: block;
}

.fullscreen-container .activation-grid {
    width: 100vw;
    height: 100vh;
    max-width: none;
    gap: 2px;
    padding: 10px;
}

.fullscreen-container .grid-cell {
    border: 1px solid #000000;
    border-radius: 4px;
}

.fullscreen-container .cell-label {
    display: none; /* Hide labels in fullscreen for filming */
}

.fullscreen-exit-hint {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: #66b3ff;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 500;
    z-index: 10000;
    border: 1px solid #0066cc;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
}

.fullscreen-exit-hint:hover {
    opacity: 1;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #1a1a1a;
    border-top: 4px solid #0080ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    color: #ffffff;
    font-size: 1.1em;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1400px) {
    .activation-grid {
        max-width: 1200px;
    }
}

@media (max-width: 1200px) {
    .activation-grid {
        max-width: 1000px;
        gap: 12px;
    }

    .container {
        padding: 15px;
    }
}

@media (max-width: 900px) {
    .activation-grid {
        grid-template-columns: repeat(4, 1fr);
        grid-template-rows: repeat(6, 1fr);
        max-width: 800px;
    }

    .color-picker-group {
        flex-direction: column;
        align-items: center;
    }

    .similarity-controls {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 600px) {
    .activation-grid {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(8, 1fr);
        gap: 8px;
    }

    .mode-controls {
        flex-direction: column;
        align-items: center;
    }

    .control-panel h1 {
        font-size: 1.8em;
    }
}