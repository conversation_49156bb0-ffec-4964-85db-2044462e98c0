/**
 * Main Application Controller
 * Coordinates all components of the Activation Map System
 */

class ActivationMapApp {
    constructor() {
        this.config = null;
        this.currentMode = 'picture';
        this.gridManager = null;
        this.colorController = null;
        this.similarityDetector = null;
        this.activationRenderer = null;
        this.isProcessing = false;
        this.isFullscreen = false;

        this.init();
    }

    async init() {
        try {
            // Load configuration
            await this.loadConfig();

            // Initialize components
            this.initializeComponents();

            // Setup event listeners
            this.setupEventListeners();

            // Initialize the grid
            await this.initializeGrid();

            console.log('Activation Map System initialized successfully');
        } catch (error) {
            console.error('Failed to initialize application:', error);
            this.showError('Failed to initialize the application. Please check the console for details.');
        }
    }

    async loadConfig() {
        try {
            const response = await fetch('config/settings.json');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.config = await response.json();
        } catch (error) {
            console.error('Failed to load configuration:', error);
            // Use default configuration
            this.config = this.getDefaultConfig();
        }
    }

    getDefaultConfig() {
        return {
            grid: { rows: 4, columns: 6, totalCells: 24 },
            paths: {
                baseImages: "assets/images/base/",
                activationImages: "assets/images/activation/",
                videos: "assets/videos/"
            },
            fileFormats: {
                images: ["jpg", "jpeg", "png"],
                videos: ["mp4", "webm", "ogg"]
            },
            defaultColors: {
                low: "#0000ff",
                medium: "#ffff00",
                high: "#ff0000"
            },
            similarity: {
                defaultTolerance: 3,
                defaultSensitivity: 0.5,
                maxTolerance: 10,
                minSensitivity: 0.1,
                maxSensitivity: 1.0,
                defaultContrast: 1.0,
                minContrast: 0.1,
                maxContrast: 3.0,
                defaultEdgeBoost: 0.0,
                minEdgeBoost: 0.0,
                maxEdgeBoost: 2.0,
                defaultColorRarity: 0.0,
                minColorRarity: 0.0,
                maxColorRarity: 1.0,
                defaultThreshold: 0.1,
                minThreshold: 0.0,
                maxThreshold: 1.0,
                useSingleActivationImage: true,
                colorSpace: "RGB"
            }
        };
    }

    initializeComponents() {
        this.gridManager = new GridManager(this.config);
        this.colorController = new ColorController(this.config.defaultColors);
        this.similarityDetector = new SimilarityDetector(this.config.similarity);
        this.activationRenderer = new ActivationRenderer();
    }

    setupEventListeners() {
        // Mode switching
        document.getElementById('pictureMode').addEventListener('click', () => {
            this.switchMode('picture');
        });

        document.getElementById('videoMode').addEventListener('click', () => {
            this.switchMode('video');
        });

        // Color controls
        document.getElementById('applyColors').addEventListener('click', () => {
            this.applyColorChanges();
        });

        // Similarity controls
        this.setupSimilarityControls();

        // Fullscreen toggle with F key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'f' || e.key === 'F') {
                e.preventDefault();
                this.toggleFullscreen();
            }
        });
    }

    async initializeGrid() {
        this.showLoading(true);

        try {
            // Create grid structure
            await this.gridManager.createGrid();

            // Load images and process activations
            await this.processAllActivations();

        } catch (error) {
            console.error('Failed to initialize grid:', error);
            this.showError('Failed to load images. Please check that your assets are properly placed.');
        } finally {
            this.showLoading(false);
        }
    }

    switchMode(mode) {
        if (this.isProcessing) return;

        this.currentMode = mode;

        // Update UI
        document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
        document.getElementById(mode + 'Mode').classList.add('active');

        // Switch grid mode
        this.gridManager.switchMode(mode);
    }

    async applyColorChanges() {
        if (this.isProcessing) return;

        const colors = this.colorController.getCurrentColors();
        await this.reprocessActivations(colors);
    }

    async processAllActivations() {
        const colors = this.colorController.getCurrentColors();

        for (let i = 1; i <= this.config.grid.totalCells; i++) {
            try {
                await this.processActivationForCell(i, colors);
            } catch (error) {
                console.warn(`Failed to process activation for cell ${i}:`, error);
            }
        }
    }

    async processActivationForCell(cellIndex, colors) {
        const baseImagePath = await this.findImageFile(this.config.paths.baseImages, `base${cellIndex}`);

        // Use single activation image if configured, otherwise use per-cell activation images
        let activationImagePath;
        if (this.config.similarity.useSingleActivationImage) {
            activationImagePath = await this.findImageFile(this.config.paths.activationImages, `activation`);
        } else {
            activationImagePath = await this.findImageFile(this.config.paths.activationImages, `activation${cellIndex}`);
        }

        if (!baseImagePath || !activationImagePath) {
            console.warn(`Missing images for cell ${cellIndex}`);
            return;
        }

        // Load images
        const baseImage = await this.loadImage(baseImagePath);
        const activationImage = await this.loadImage(activationImagePath);

        // Calculate similarity map
        const similarityMap = await this.similarityDetector.calculateSimilarity(baseImage, activationImage);

        // Render activation overlay
        const overlayCanvas = this.activationRenderer.createActivationOverlay(similarityMap, colors);

        // Apply to grid cell
        this.gridManager.setActivationOverlay(cellIndex, overlayCanvas);
    }

    async findImageFile(basePath, filename) {
        for (const ext of this.config.fileFormats.images) {
            const path = `${basePath}${filename}.${ext}`;
            if (await this.fileExists(path)) {
                return path;
            }
        }
        return null;
    }

    async fileExists(path) {
        try {
            const response = await fetch(path, { method: 'HEAD' });
            return response.ok;
        } catch {
            return false;
        }
    }

    loadImage(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            img.onload = () => resolve(img);
            img.onerror = reject;
            img.src = src;
        });
    }

    async reprocessActivations(colors) {
        this.showLoading(true);

        try {
            for (let i = 1; i <= this.config.grid.totalCells; i++) {
                await this.processActivationForCell(i, colors);
            }
        } catch (error) {
            console.error('Failed to reprocess activations:', error);
        } finally {
            this.showLoading(false);
        }
    }

    debounceReprocess() {
        clearTimeout(this.reprocessTimeout);
        this.reprocessTimeout = setTimeout(() => {
            const colors = this.colorController.getCurrentColors();
            this.reprocessActivations(colors);
        }, 500);
    }

    showLoading(show) {
        this.isProcessing = show;
        const overlay = document.getElementById('loadingOverlay');
        if (show) {
            overlay.classList.add('active');
        } else {
            overlay.classList.remove('active');
        }
    }

    showError(message) {
        alert(message); // Simple error display - could be enhanced with a custom modal
    }

    toggleFullscreen() {
        const fullscreenContainer = document.getElementById('fullscreenContainer');
        const fullscreenGrid = document.getElementById('fullscreenGrid');
        const mainGrid = document.getElementById('activationGrid');

        if (!this.isFullscreen) {
            // Enter fullscreen
            this.isFullscreen = true;

            // Clone the main grid to fullscreen container
            fullscreenGrid.innerHTML = mainGrid.innerHTML;

            // Copy activation overlay canvases (innerHTML doesn't copy canvas content)
            this.copyActivationOverlays(mainGrid, fullscreenGrid);

            // Show fullscreen container
            fullscreenContainer.classList.add('active');

            // Hide main container
            document.querySelector('.container').style.display = 'none';

            // Sync video playback if in video mode
            if (this.currentMode === 'video') {
                this.syncVideoPlayback(mainGrid, fullscreenGrid);
            }

        } else {
            // Exit fullscreen
            this.isFullscreen = false;

            // Hide fullscreen container
            fullscreenContainer.classList.remove('active');

            // Show main container
            document.querySelector('.container').style.display = 'block';

            // Clear fullscreen grid
            fullscreenGrid.innerHTML = '';
        }
    }

    copyActivationOverlays(sourceGrid, targetGrid) {
        const sourceCells = sourceGrid.querySelectorAll('.grid-cell');
        const targetCells = targetGrid.querySelectorAll('.grid-cell');

        sourceCells.forEach((sourceCell, index) => {
            const targetCell = targetCells[index];
            if (sourceCell && targetCell) {
                const sourceCanvas = sourceCell.querySelector('.activation-overlay');
                const targetCanvas = targetCell.querySelector('.activation-overlay');

                if (sourceCanvas && targetCanvas) {
                    // Copy canvas dimensions
                    targetCanvas.width = sourceCanvas.width;
                    targetCanvas.height = sourceCanvas.height;

                    // Copy canvas content
                    const targetCtx = targetCanvas.getContext('2d');
                    targetCtx.clearRect(0, 0, targetCanvas.width, targetCanvas.height);
                    targetCtx.drawImage(sourceCanvas, 0, 0);
                }

                // Add click event listener to fullscreen cells
                targetCell.addEventListener('click', () => {
                    const cellIndex = parseInt(sourceCell.getAttribute('data-cell-index'));
                    this.gridManager.openCellInNewTab(cellIndex);
                });
            }
        });
    }

    syncVideoPlayback(sourceGrid, targetGrid) {
        const sourceVideos = sourceGrid.querySelectorAll('video');
        const targetVideos = targetGrid.querySelectorAll('video');

        sourceVideos.forEach((sourceVideo, index) => {
            const targetVideo = targetVideos[index];
            if (sourceVideo && targetVideo) {
                targetVideo.currentTime = sourceVideo.currentTime;
                if (!sourceVideo.paused) {
                    targetVideo.play().catch(e => console.warn('Video sync failed:', e));
                }
            }
        });
    }

    setupSimilarityControls() {
        // Tolerance control
        const toleranceSlider = document.getElementById('tolerance');
        toleranceSlider.addEventListener('input', (e) => {
            document.getElementById('toleranceValue').textContent = e.target.value;
            this.similarityDetector.setTolerance(parseInt(e.target.value));
            this.debounceReprocess();
        });

        // Sensitivity control
        const sensitivitySlider = document.getElementById('sensitivity');
        sensitivitySlider.addEventListener('input', (e) => {
            document.getElementById('sensitivityValue').textContent = e.target.value;
            this.similarityDetector.setSensitivity(parseFloat(e.target.value));
            this.debounceReprocess();
        });

        // Contrast control
        const contrastSlider = document.getElementById('contrast');
        contrastSlider.addEventListener('input', (e) => {
            document.getElementById('contrastValue').textContent = e.target.value;
            this.similarityDetector.setContrast(parseFloat(e.target.value));
            this.debounceReprocess();
        });

        // Edge Boost control
        const edgeBoostSlider = document.getElementById('edgeBoost');
        edgeBoostSlider.addEventListener('input', (e) => {
            document.getElementById('edgeBoostValue').textContent = e.target.value;
            this.similarityDetector.setEdgeBoost(parseFloat(e.target.value));
            this.debounceReprocess();
        });

        // Color Rarity control
        const colorRaritySlider = document.getElementById('colorRarity');
        colorRaritySlider.addEventListener('input', (e) => {
            document.getElementById('colorRarityValue').textContent = e.target.value;
            this.similarityDetector.setColorRarity(parseFloat(e.target.value));
            this.debounceReprocess();
        });

        // Threshold control
        const thresholdSlider = document.getElementById('threshold');
        thresholdSlider.addEventListener('input', (e) => {
            document.getElementById('thresholdValue').textContent = e.target.value;
            this.similarityDetector.setThreshold(parseFloat(e.target.value));
            this.debounceReprocess();
        });

        // Color Space control
        const colorSpaceSelect = document.getElementById('colorSpace');
        colorSpaceSelect.addEventListener('change', (e) => {
            this.similarityDetector.setColorSpace(e.target.value);
            this.debounceReprocess();
        });
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ActivationMapApp();
});