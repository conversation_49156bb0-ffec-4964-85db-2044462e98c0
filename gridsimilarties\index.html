<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activation Map System</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <div class="container">
        <header class="control-panel">
            <h1>Activation Map System</h1>

            <div class="mode-controls">
                <button id="pictureMode" class="mode-btn active">Picture Mode</button>
                <button id="videoMode" class="mode-btn">Video Mode</button>
                <div class="fullscreen-hint">Press <strong>F</strong> for fullscreen</div>
            </div>

            <div class="color-controls">
                <h3>Activation Colors</h3>
                <div class="color-picker-group">
                    <div class="color-input">
                        <label for="color1">Low Activation:</label>
                        <input type="color" id="color1" value="#0000ff">
                    </div>
                    <div class="color-input">
                        <label for="color2">Medium Activation:</label>
                        <input type="color" id="color2" value="#ffff00">
                    </div>
                    <div class="color-input">
                        <label for="color3">High Activation:</label>
                        <input type="color" id="color3" value="#ff0000">
                    </div>
                </div>
                <button id="applyColors">Apply Colors</button>
            </div>

            <div class="similarity-controls">
                <h3>Similarity Settings</h3>
                <div class="slider-group">
                    <label for="tolerance">Spatial Tolerance:</label>
                    <input type="range" id="tolerance" min="1" max="10" value="3">
                    <span id="toleranceValue">3</span>
                </div>
                <div class="slider-group">
                    <label for="sensitivity">Sensitivity:</label>
                    <input type="range" id="sensitivity" min="0.1" max="1.0" step="0.1" value="0.5">
                    <span id="sensitivityValue">0.5</span>
                </div>
                <div class="slider-group">
                    <label for="contrast">Contrast:</label>
                    <input type="range" id="contrast" min="0.1" max="3.0" step="0.1" value="1.0">
                    <span id="contrastValue">1.0</span>
                </div>
                <div class="slider-group">
                    <label for="edgeBoost">Edge Boost:</label>
                    <input type="range" id="edgeBoost" min="0.0" max="2.0" step="0.1" value="0.0">
                    <span id="edgeBoostValue">0.0</span>
                </div>
                <div class="slider-group">
                    <label for="colorRarity">Color Rarity:</label>
                    <input type="range" id="colorRarity" min="0.0" max="1.0" step="0.1" value="0.0">
                    <span id="colorRarityValue">0.0</span>
                </div>
                <div class="slider-group">
                    <label for="threshold">Threshold:</label>
                    <input type="range" id="threshold" min="0.0" max="1.0" step="0.05" value="0.1">
                    <span id="thresholdValue">0.1</span>
                </div>
                <div class="slider-group">
                    <label for="colorSpace">Color Space:</label>
                    <select id="colorSpace">
                        <option value="RGB">RGB</option>
                        <option value="HSV">HSV</option>
                    </select>
                </div>
                <div class="slider-group">
                    <label for="activationMode">Activation Mode:</label>
                    <select id="activationMode">
                        <option value="single">Single Image (activation1)</option>
                        <option value="multiple">Multiple Images (activation1-24)</option>
                    </select>
                </div>
            </div>
        </header>

        <main class="grid-container">
            <div id="activationGrid" class="activation-grid">
                <!-- Grid cells will be generated dynamically -->
            </div>
        </main>

        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
            <p>Processing activation maps...</p>
        </div>
    </div>

    <!-- Fullscreen Container -->
    <div class="fullscreen-container" id="fullscreenContainer">
        <div class="fullscreen-exit-hint">Press F to exit fullscreen</div>
        <div id="fullscreenGrid" class="activation-grid">
            <!-- Grid will be cloned here for fullscreen -->
        </div>
    </div>

    <script src="js/colorController.js"></script>
    <script src="js/similarityDetector.js"></script>
    <script src="js/activationRenderer.js"></script>
    <script src="js/gridManager.js"></script>
    <script src="js/main.js"></script>
</body>
</html>