/**
 * Grid Manager
 * Handles the creation and management of the 4x6 activation grid
 */

class GridManager {
    constructor(config) {
        this.config = config;
        this.gridContainer = document.getElementById('activationGrid');
        this.cells = new Map();
        this.currentMode = 'picture';
    }

    async createGrid() {
        this.gridContainer.innerHTML = '';

        for (let i = 1; i <= this.config.grid.totalCells; i++) {
            const cell = await this.createGridCell(i);
            this.cells.set(i, cell);
            this.gridContainer.appendChild(cell.element);
        }
    }

    async createGridCell(index) {
        const cellElement = document.createElement('div');
        cellElement.className = 'grid-cell';
        cellElement.setAttribute('data-cell-index', index);

        // Create cell label
        const label = document.createElement('div');
        label.className = 'cell-label';
        label.textContent = `Cell ${index}`;
        cellElement.appendChild(label);

        // Create image element
        const img = document.createElement('img');
        img.className = 'cell-image';
        img.alt = `Base image ${index}`;

        // Try to load base image with new naming convention
        const baseImagePath = await this.findImageFile(this.config.paths.baseImages, `base${index}`);
        if (baseImagePath) {
            img.src = baseImagePath;
        } else {
            // Create placeholder if no image found
            img.src = this.createPlaceholderImage(index);
        }

        cellElement.appendChild(img);

        // Create video element
        const video = document.createElement('video');
        video.className = 'cell-video';
        video.muted = this.config.video.muted;
        video.loop = this.config.video.loop;
        video.autoplay = this.config.video.autoplay;
        video.controls = this.config.video.controls;

        // Try to load video
        const videoPath = await this.findVideoFile(this.config.paths.videos, 'main');
        if (videoPath) {
            video.src = videoPath;
        }

        cellElement.appendChild(video);

        // Create activation overlay canvas
        const overlayCanvas = document.createElement('canvas');
        overlayCanvas.className = 'activation-overlay';
        cellElement.appendChild(overlayCanvas);

        // Add click event listener for opening in new tab
        cellElement.addEventListener('click', () => {
            this.openCellInNewTab(index);
        });

        return {
            element: cellElement,
            image: img,
            video: video,
            overlay: overlayCanvas,
            index: index
        };
    }

    async findImageFile(basePath, filename) {
        for (const ext of this.config.fileFormats.images) {
            const path = `${basePath}${filename}.${ext}`;
            if (await this.fileExists(path)) {
                return path;
            }
        }
        return null;
    }

    async findVideoFile(basePath, filename) {
        for (const ext of this.config.fileFormats.videos) {
            const path = `${basePath}${filename}.${ext}`;
            if (await this.fileExists(path)) {
                return path;
            }
        }
        return null;
    }

    async fileExists(path) {
        try {
            const response = await fetch(path, { method: 'HEAD' });
            return response.ok;
        } catch {
            return false;
        }
    }

    createPlaceholderImage(index) {
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 400;
        const ctx = canvas.getContext('2d');

        // Create gradient background
        const gradient = ctx.createLinearGradient(0, 0, 400, 400);
        gradient.addColorStop(0, '#333333');
        gradient.addColorStop(1, '#555555');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 400, 400);

        // Add text
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(`Base Image ${index}`, 200, 180);
        ctx.fillText('Not Found', 200, 220);

        return canvas.toDataURL();
    }

    switchMode(mode) {
        this.currentMode = mode;

        this.cells.forEach((cell) => {
            if (mode === 'video') {
                cell.element.classList.add('video-mode');
                if (cell.video.src) {
                    cell.video.play().catch(e => console.warn('Video autoplay failed:', e));
                }
            } else {
                cell.element.classList.remove('video-mode');
                if (cell.video.src) {
                    cell.video.pause();
                }
            }
        });

        // Also update fullscreen grid if active
        this.updateFullscreenMode(mode);
    }

    updateFullscreenMode(mode) {
        const fullscreenGrid = document.getElementById('fullscreenGrid');
        if (fullscreenGrid) {
            const fullscreenCells = fullscreenGrid.querySelectorAll('.grid-cell');
            fullscreenCells.forEach((cell) => {
                const video = cell.querySelector('video');
                if (mode === 'video') {
                    cell.classList.add('video-mode');
                    if (video && video.src) {
                        video.play().catch(e => console.warn('Fullscreen video autoplay failed:', e));
                    }
                } else {
                    cell.classList.remove('video-mode');
                    if (video && video.src) {
                        video.pause();
                    }
                }
            });
        }
    }

    setActivationOverlay(cellIndex, overlayCanvas) {
        const cell = this.cells.get(cellIndex);
        if (!cell) return;

        const targetCanvas = cell.overlay;
        const targetCtx = targetCanvas.getContext('2d');

        // Set canvas size to match cell dimensions exactly
        const cellRect = cell.element.getBoundingClientRect();
        const cellWidth = cellRect.width || 200;
        const cellHeight = cellRect.height || 200;

        targetCanvas.width = cellWidth;
        targetCanvas.height = cellHeight;

        // Clear and draw the activation overlay to fit the entire cell
        targetCtx.clearRect(0, 0, cellWidth, cellHeight);

        // Use object-fit: cover behavior - scale to fill while maintaining aspect ratio
        const overlayAspect = overlayCanvas.width / overlayCanvas.height;
        const cellAspect = cellWidth / cellHeight;

        let drawWidth, drawHeight, drawX, drawY;

        if (overlayAspect > cellAspect) {
            // Overlay is wider - fit to height and crop width
            drawHeight = cellHeight;
            drawWidth = cellHeight * overlayAspect;
            drawX = (cellWidth - drawWidth) / 2;
            drawY = 0;
        } else {
            // Overlay is taller - fit to width and crop height
            drawWidth = cellWidth;
            drawHeight = cellWidth / overlayAspect;
            drawX = 0;
            drawY = (cellHeight - drawHeight) / 2;
        }

        targetCtx.drawImage(overlayCanvas, drawX, drawY, drawWidth, drawHeight);

        // Also update fullscreen overlay if fullscreen is active
        this.updateFullscreenOverlay(cellIndex, overlayCanvas);
    }

    updateFullscreenOverlay(cellIndex, overlayCanvas) {
        const fullscreenGrid = document.getElementById('fullscreenGrid');
        if (!fullscreenGrid || !fullscreenGrid.children.length) return;

        const fullscreenCells = fullscreenGrid.querySelectorAll('.grid-cell');
        const targetCell = fullscreenCells[cellIndex - 1]; // cellIndex is 1-based

        if (targetCell) {
            const targetCanvas = targetCell.querySelector('.activation-overlay');
            if (targetCanvas) {
                const targetCtx = targetCanvas.getContext('2d');

                // Set canvas size to match cell dimensions exactly
                const cellRect = targetCell.getBoundingClientRect();
                const cellWidth = cellRect.width || 200;
                const cellHeight = cellRect.height || 200;

                targetCanvas.width = cellWidth;
                targetCanvas.height = cellHeight;

                // Clear and draw the activation overlay to fit the entire cell
                targetCtx.clearRect(0, 0, cellWidth, cellHeight);

                // Use object-fit: cover behavior - scale to fill while maintaining aspect ratio
                const overlayAspect = overlayCanvas.width / overlayCanvas.height;
                const cellAspect = cellWidth / cellHeight;

                let drawWidth, drawHeight, drawX, drawY;

                if (overlayAspect > cellAspect) {
                    // Overlay is wider - fit to height and crop width
                    drawHeight = cellHeight;
                    drawWidth = cellHeight * overlayAspect;
                    drawX = (cellWidth - drawWidth) / 2;
                    drawY = 0;
                } else {
                    // Overlay is taller - fit to width and crop height
                    drawWidth = cellWidth;
                    drawHeight = cellWidth / overlayAspect;
                    drawX = 0;
                    drawY = (cellHeight - drawHeight) / 2;
                }

                targetCtx.drawImage(overlayCanvas, drawX, drawY, drawWidth, drawHeight);
            }
        }
    }

    getCell(index) {
        return this.cells.get(index);
    }

    getAllCells() {
        return Array.from(this.cells.values());
    }

    updateCellImage(index, imageSrc) {
        const cell = this.cells.get(index);
        if (cell) {
            cell.image.src = imageSrc;
        }
    }

    updateCellVideo(index, videoSrc) {
        const cell = this.cells.get(index);
        if (cell) {
            cell.video.src = videoSrc;
            if (this.currentMode === 'video') {
                cell.video.play().catch(e => console.warn('Video play failed:', e));
            }
        }
    }

    clearActivationOverlay(cellIndex) {
        const cell = this.cells.get(cellIndex);
        if (cell) {
            const ctx = cell.overlay.getContext('2d');
            ctx.clearRect(0, 0, cell.overlay.width, cell.overlay.height);
        }
    }

    clearAllActivationOverlays() {
        this.cells.forEach((_, index) => {
            this.clearActivationOverlay(index);
        });
    }

    openCellInNewTab(cellIndex) {
        // Open the comprehensive image viewer starting at the specified cell
        this.openImageViewer(cellIndex);
    }

    async openImageViewer(startIndex = 1) {
        const newWindow = window.open('', '_blank');
        if (!newWindow) return;

        // Create the viewer HTML with navigation
        newWindow.document.write(`
            <!DOCTYPE html>
            <html>
                <head>
                    <title>Activation Map Viewer</title>
                    <style>
                        body {
                            margin: 0;
                            padding: 0;
                            background: #000;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            min-height: 100vh;
                            font-family: 'Gill Sans', sans-serif;
                            overflow: hidden;
                        }
                        .viewer-container {
                            position: relative;
                            width: 100vw;
                            height: 100vh;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }
                        .image-display {
                            width: 90vw;
                            height: 90vh;
                            object-fit: contain;
                            transition: opacity 0.3s ease;
                        }
                        .loading {
                            opacity: 0.5;
                        }
                        .info-panel {
                            position: absolute;
                            top: 20px;
                            left: 20px;
                            color: #66b3ff;
                            z-index: 10;
                            background: rgba(0, 0, 0, 0.7);
                            padding: 15px;
                            border-radius: 8px;
                            border: 1px solid #0066cc;
                        }
                        .info-panel h1 {
                            margin: 0 0 10px 0;
                            font-size: 1.2em;
                        }
                        .info-panel p {
                            margin: 5px 0;
                            font-size: 0.9em;
                            color: #80ccff;
                        }
                        .navigation-hint {
                            position: absolute;
                            bottom: 20px;
                            left: 50%;
                            transform: translateX(-50%);
                            color: #66b3ff;
                            background: rgba(0, 0, 0, 0.7);
                            padding: 10px 20px;
                            border-radius: 20px;
                            border: 1px solid #0066cc;
                            font-size: 0.9em;
                        }
                        .error-message {
                            color: #ff6666;
                            text-align: center;
                            font-size: 1.1em;
                        }
                    </style>
                </head>
                <body>
                    <div class="viewer-container">
                        <div class="info-panel">
                            <h1 id="cellTitle">Loading...</h1>
                            <p id="cellInfo">Preparing activation map...</p>
                            <p id="navigationInfo">Use ← → arrow keys to navigate</p>
                        </div>
                        <img id="mainImage" class="image-display loading" alt="Activation Map" />
                        <div class="navigation-hint">
                            Use <strong>←</strong> and <strong>→</strong> arrow keys to navigate between cells
                        </div>
                    </div>
                </body>
            </html>
        `);

        // Wait for the document to be ready
        await new Promise(resolve => {
            if (newWindow.document.readyState === 'complete') {
                resolve();
            } else {
                newWindow.addEventListener('load', resolve);
            }
        });

        // Initialize the viewer functionality
        this.initializeViewer(newWindow, startIndex);
    }

    async initializeViewer(viewerWindow, startIndex) {
        let currentIndex = startIndex;
        const totalCells = this.config.grid.totalCells;

        const mainImage = viewerWindow.document.getElementById('mainImage');
        const cellTitle = viewerWindow.document.getElementById('cellTitle');
        const cellInfo = viewerWindow.document.getElementById('cellInfo');

        // Function to render current cell
        const renderCurrentCell = async () => {
            try {
                cellTitle.textContent = `Cell ${currentIndex}`;
                cellInfo.textContent = `Loading cell ${currentIndex} of ${totalCells}...`;
                mainImage.classList.add('loading');

                const imageData = await this.renderCellToDataURL(currentIndex);

                if (imageData) {
                    mainImage.src = imageData;
                    mainImage.classList.remove('loading');
                    cellInfo.textContent = `Cell ${currentIndex} of ${totalCells} - ${this.currentMode} mode`;
                } else {
                    // Fallback: try to show just the base image
                    const cell = this.cells.get(currentIndex);
                    if (cell && cell.image && cell.image.src) {
                        mainImage.src = cell.image.src;
                        mainImage.classList.remove('loading');
                        cellInfo.textContent = `Cell ${currentIndex} - Base image only (no activation overlay)`;
                    } else {
                        cellInfo.textContent = `Cell ${currentIndex} - No data available`;
                        mainImage.src = '';
                        mainImage.classList.remove('loading');
                    }
                }
            } catch (error) {
                console.error('Error rendering cell:', error);
                cellInfo.textContent = `Error loading cell ${currentIndex}`;
                mainImage.classList.remove('loading');

                // Try fallback to base image
                const cell = this.cells.get(currentIndex);
                if (cell && cell.image && cell.image.src) {
                    mainImage.src = cell.image.src;
                    cellInfo.textContent = `Cell ${currentIndex} - Base image only (rendering error)`;
                }
            }
        };

        // Navigation function
        const navigate = (direction) => {
            if (direction === 'next' && currentIndex < totalCells) {
                currentIndex++;
                renderCurrentCell();
            } else if (direction === 'prev' && currentIndex > 1) {
                currentIndex--;
                renderCurrentCell();
            }
        };

        // Add keyboard event listener
        viewerWindow.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                    e.preventDefault();
                    navigate('next');
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    navigate('prev');
                    break;
                case 'Escape':
                    viewerWindow.close();
                    break;
            }
        });

        // Focus the window to receive keyboard events
        viewerWindow.focus();

        // Render the initial cell
        await renderCurrentCell();
    }

    async renderCellToDataURL(cellIndex) {
        const cell = this.cells.get(cellIndex);
        if (!cell) return null;

        // Create a high-resolution canvas for better quality
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Use high resolution - aim for at least 2048x2048 or use source image dimensions
        const cellRect = cell.element.getBoundingClientRect();
        const aspectRatio = cellRect.width / cellRect.height || 1;

        // Set high resolution dimensions
        const targetSize = 2048;
        if (aspectRatio >= 1) {
            canvas.width = targetSize;
            canvas.height = targetSize / aspectRatio;
        } else {
            canvas.width = targetSize * aspectRatio;
            canvas.height = targetSize;
        }

        // Enable high-quality rendering
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        // Determine which content to capture based on current mode
        let sourceElement;
        if (this.currentMode === 'video' && cell.video.src) {
            sourceElement = cell.video;
        } else {
            sourceElement = cell.image;
        }

        // Draw the base content at high resolution
        if (sourceElement && (sourceElement.complete || sourceElement.readyState >= 2)) {
            // Calculate object-fit: cover positioning
            const sourceAspect = sourceElement.naturalWidth / sourceElement.naturalHeight || sourceElement.videoWidth / sourceElement.videoHeight || 1;
            const canvasAspect = canvas.width / canvas.height;

            let drawWidth, drawHeight, drawX, drawY;

            if (sourceAspect > canvasAspect) {
                // Source is wider - fit to height and crop width
                drawHeight = canvas.height;
                drawWidth = canvas.height * sourceAspect;
                drawX = (canvas.width - drawWidth) / 2;
                drawY = 0;
            } else {
                // Source is taller - fit to width and crop height
                drawWidth = canvas.width;
                drawHeight = canvas.width / sourceAspect;
                drawX = 0;
                drawY = (canvas.height - drawHeight) / 2;
            }

            ctx.drawImage(sourceElement, drawX, drawY, drawWidth, drawHeight);
        }

        // Draw the activation overlay on top if it exists and has content
        try {
            if (cell.overlay && cell.overlay.width > 0 && cell.overlay.height > 0) {
                // Check if overlay has actual content
                const overlayCtx = cell.overlay.getContext('2d');
                const overlayData = overlayCtx.getImageData(0, 0, cell.overlay.width, cell.overlay.height);
                const hasContent = overlayData.data.some(value => value > 0);

                if (hasContent) {
                    ctx.globalCompositeOperation = 'multiply';
                    ctx.globalAlpha = 0.7;
                    ctx.drawImage(cell.overlay, 0, 0, canvas.width, canvas.height);
                }
            }
        } catch (error) {
            console.warn('Could not render overlay for cell', cellIndex, error);
        }

        // Convert canvas to high-quality PNG data URL
        return canvas.toDataURL('image/png', 1.0);
    }
}