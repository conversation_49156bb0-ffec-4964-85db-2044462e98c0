/**
 * Similarity Detector
 * Implements algorithmic similarity detection with spatial tolerance
 */

class SimilarityDetector {
    constructor(config) {
        this.tolerance = config.defaultTolerance || 3;
        this.sensitivity = config.defaultSensitivity || 0.5;
        this.maxTolerance = config.maxTolerance || 10;
        this.minSensitivity = config.minSensitivity || 0.1;
        this.maxSensitivity = config.maxSensitivity || 1.0;

        // New enhanced parameters
        this.contrast = config.defaultContrast || 1.0;
        this.minContrast = config.minContrast || 0.1;
        this.maxContrast = config.maxContrast || 3.0;

        this.edgeBoost = config.defaultEdgeBoost || 0.0;
        this.minEdgeBoost = config.minEdgeBoost || 0.0;
        this.maxEdgeBoost = config.maxEdgeBoost || 2.0;

        this.colorRarity = config.defaultColorRarity || 0.0;
        this.minColorRarity = config.minColorRarity || 0.0;
        this.maxColorRarity = config.maxColorRarity || 1.0;

        this.threshold = config.defaultThreshold || 0.1;
        this.minThreshold = config.minThreshold || 0.0;
        this.maxThreshold = config.maxThreshold || 1.0;

        this.colorSpace = config.colorSpace || 'RGB';

        // Cache for color distribution analysis
        this.colorDistributionCache = new Map();
    }

    setTolerance(tolerance) {
        this.tolerance = Math.max(1, Math.min(this.maxTolerance, tolerance));
    }

    setSensitivity(sensitivity) {
        this.sensitivity = Math.max(this.minSensitivity, Math.min(this.maxSensitivity, sensitivity));
    }

    setContrast(contrast) {
        this.contrast = Math.max(this.minContrast, Math.min(this.maxContrast, contrast));
    }

    setEdgeBoost(edgeBoost) {
        this.edgeBoost = Math.max(this.minEdgeBoost, Math.min(this.maxEdgeBoost, edgeBoost));
    }

    setColorRarity(colorRarity) {
        this.colorRarity = Math.max(this.minColorRarity, Math.min(this.maxColorRarity, colorRarity));
    }

    setThreshold(threshold) {
        this.threshold = Math.max(this.minThreshold, Math.min(this.maxThreshold, threshold));
    }

    setColorSpace(colorSpace) {
        this.colorSpace = colorSpace;
        // Clear cache when color space changes
        this.colorDistributionCache.clear();
    }

    /**
     * Calculate similarity map between base image and activation image
     * Returns a 2D array of similarity values (0-1)
     */
    async calculateSimilarity(baseImage, activationImage) {
        // Create canvases for image processing
        const baseCanvas = this.createCanvas(baseImage);
        const activationCanvas = this.createCanvas(activationImage);

        const baseCtx = baseCanvas.getContext('2d');
        const activationCtx = activationCanvas.getContext('2d');

        // Draw images to canvases
        baseCtx.drawImage(baseImage, 0, 0, baseCanvas.width, baseCanvas.height);
        activationCtx.drawImage(activationImage, 0, 0, activationCanvas.width, activationCanvas.height);

        // Get image data
        const baseData = baseCtx.getImageData(0, 0, baseCanvas.width, baseCanvas.height);
        const activationData = activationCtx.getImageData(0, 0, activationCanvas.width, activationCanvas.height);

        // Analyze color distribution for rarity weighting
        const colorDistribution = this.analyzeColorDistribution(baseData, baseCanvas.width, baseCanvas.height);

        // Calculate edge map for edge boost
        const edgeMap = this.calculateEdgeMap(baseData, baseCanvas.width, baseCanvas.height);

        // Calculate similarity map with enhanced features
        return this.computeSimilarityMap(baseData, activationData, baseCanvas.width, baseCanvas.height, colorDistribution, edgeMap);
    }

    createCanvas(image, maxSize = 512) {
        const canvas = document.createElement('canvas');

        // Calculate dimensions maintaining aspect ratio
        let { width, height } = this.calculateDimensions(image.width, image.height, maxSize);

        canvas.width = width;
        canvas.height = height;

        return canvas;
    }

    calculateDimensions(originalWidth, originalHeight, maxSize) {
        const aspectRatio = originalWidth / originalHeight;

        let width, height;
        if (originalWidth > originalHeight) {
            width = Math.min(originalWidth, maxSize);
            height = width / aspectRatio;
        } else {
            height = Math.min(originalHeight, maxSize);
            width = height * aspectRatio;
        }

        return {
            width: Math.round(width),
            height: Math.round(height)
        };
    }

    computeSimilarityMap(baseData, activationData, width, height, colorDistribution = null, edgeMap = null) {
        const similarityMap = [];

        for (let y = 0; y < height; y++) {
            const row = [];
            for (let x = 0; x < width; x++) {
                const similarity = this.calculatePixelSimilarity(
                    baseData, activationData, x, y, width, height, colorDistribution, edgeMap
                );
                row.push(similarity);
            }
            similarityMap.push(row);
        }

        // Apply threshold filter
        return this.applyThreshold(similarityMap);
    }

    calculatePixelSimilarity(baseData, activationData, x, y, width, height, colorDistribution = null, edgeMap = null) {
        const basePixel = this.getPixelRGB(baseData, x, y, width);
        let maxSimilarity = 0;

        // Check pixels within tolerance radius
        const tolerance = this.tolerance;
        for (let dy = -tolerance; dy <= tolerance; dy++) {
            for (let dx = -tolerance; dx <= tolerance; dx++) {
                const nx = x + dx;
                const ny = y + dy;

                // Check bounds
                if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                    const activationPixel = this.getPixelRGB(activationData, nx, ny, width);

                    // Choose color similarity method based on color space
                    const similarity = this.colorSpace === 'HSV'
                        ? this.calculateColorSimilarityHSV(basePixel, activationPixel)
                        : this.calculateColorSimilarity(basePixel, activationPixel);

                    maxSimilarity = Math.max(maxSimilarity, similarity);
                }
            }
        }

        // Apply enhancements
        maxSimilarity = this.applyEnhancements(maxSimilarity, basePixel, x, y, colorDistribution, edgeMap);

        // Apply sensitivity adjustment
        return this.applySensitivity(maxSimilarity);
    }

    getPixelRGB(imageData, x, y, width) {
        const index = (y * width + x) * 4;
        return {
            r: imageData.data[index],
            g: imageData.data[index + 1],
            b: imageData.data[index + 2],
            a: imageData.data[index + 3]
        };
    }

    calculateColorSimilarity(color1, color2) {
        // Use Euclidean distance in RGB space
        const dr = color1.r - color2.r;
        const dg = color1.g - color2.g;
        const db = color1.b - color2.b;

        const distance = Math.sqrt(dr * dr + dg * dg + db * db);
        const maxDistance = Math.sqrt(255 * 255 * 3); // Maximum possible distance

        // Convert distance to similarity (0-1, where 1 is identical)
        return 1 - (distance / maxDistance);
    }

    applySensitivity(similarity) {
        // Apply sensitivity curve
        // Higher sensitivity makes more pixels activate
        const adjusted = Math.pow(similarity, 1 / this.sensitivity);
        return Math.max(0, Math.min(1, adjusted));
    }

    /**
     * Alternative similarity calculation using HSV color space
     */
    calculateColorSimilarityHSV(color1, color2) {
        const hsv1 = this.rgbToHsv(color1);
        const hsv2 = this.rgbToHsv(color2);

        // Weight different components differently
        const hueWeight = 0.4;
        const satWeight = 0.3;
        const valWeight = 0.3;

        // Handle hue wraparound
        let hueDiff = Math.abs(hsv1.h - hsv2.h);
        hueDiff = Math.min(hueDiff, 360 - hueDiff) / 180; // Normalize to 0-1

        const satDiff = Math.abs(hsv1.s - hsv2.s);
        const valDiff = Math.abs(hsv1.v - hsv2.v);

        const totalDiff = hueWeight * hueDiff + satWeight * satDiff + valWeight * valDiff;
        return 1 - totalDiff;
    }

    rgbToHsv(rgb) {
        const r = rgb.r / 255;
        const g = rgb.g / 255;
        const b = rgb.b / 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        const diff = max - min;

        let h = 0;
        if (diff !== 0) {
            if (max === r) {
                h = ((g - b) / diff) % 6;
            } else if (max === g) {
                h = (b - r) / diff + 2;
            } else {
                h = (r - g) / diff + 4;
            }
        }
        h = (h * 60 + 360) % 360;

        const s = max === 0 ? 0 : diff / max;
        const v = max;

        return { h, s, v };
    }

    /**
     * Post-process similarity map with smoothing
     */
    smoothSimilarityMap(similarityMap, radius = 1) {
        const height = similarityMap.length;
        const width = similarityMap[0].length;
        const smoothed = [];

        for (let y = 0; y < height; y++) {
            const row = [];
            for (let x = 0; x < width; x++) {
                let sum = 0;
                let count = 0;

                for (let dy = -radius; dy <= radius; dy++) {
                    for (let dx = -radius; dx <= radius; dx++) {
                        const ny = y + dy;
                        const nx = x + dx;

                        if (ny >= 0 && ny < height && nx >= 0 && nx < width) {
                            sum += similarityMap[ny][nx];
                            count++;
                        }
                    }
                }

                row.push(sum / count);
            }
            smoothed.push(row);
        }

        return smoothed;
    }

    /**
     * Apply threshold to similarity map
     */
    applySimilarityThreshold(similarityMap, threshold = 0.3) {
        return similarityMap.map(row =>
            row.map(value => value >= threshold ? value : 0)
        );
    }

    /**
     * Apply threshold filter using current threshold setting
     */
    applyThreshold(similarityMap) {
        if (this.threshold <= 0) return similarityMap;

        return similarityMap.map(row =>
            row.map(value => value >= this.threshold ? value : 0)
        );
    }

    /**
     * Apply various enhancements to similarity value
     */
    applyEnhancements(similarity, basePixel, x, y, colorDistribution, edgeMap) {
        let enhanced = similarity;

        // Apply contrast adjustment
        if (this.contrast !== 1.0) {
            enhanced = this.applyContrastAdjustment(enhanced);
        }

        // Apply color rarity weighting
        if (this.colorRarity > 0 && colorDistribution) {
            enhanced = this.applyColorRarityWeighting(enhanced, basePixel, colorDistribution);
        }

        // Apply edge boost
        if (this.edgeBoost > 0 && edgeMap) {
            enhanced = this.applyEdgeBoost(enhanced, x, y, edgeMap);
        }

        return Math.max(0, Math.min(1, enhanced));
    }

    /**
     * Apply contrast adjustment to similarity value
     */
    applyContrastAdjustment(similarity) {
        // Apply contrast using a power curve
        const centered = similarity - 0.5;
        const contrasted = Math.sign(centered) * Math.pow(Math.abs(centered * 2), 1 / this.contrast) / 2;
        return contrasted + 0.5;
    }

    /**
     * Apply color rarity weighting - boost rare colors
     */
    applyColorRarityWeighting(similarity, pixel, colorDistribution) {
        const colorKey = this.getColorKey(pixel);
        const frequency = colorDistribution.get(colorKey) || 0;
        const totalPixels = colorDistribution.get('_total') || 1;
        const rarity = 1 - (frequency / totalPixels);

        // Boost similarity for rare colors
        const rarityBoost = 1 + (rarity * this.colorRarity);
        return similarity * rarityBoost;
    }

    /**
     * Apply edge boost - enhance similarities near edges
     */
    applyEdgeBoost(similarity, x, y, edgeMap) {
        const edgeStrength = edgeMap[y] && edgeMap[y][x] ? edgeMap[y][x] : 0;
        const boost = 1 + (edgeStrength * this.edgeBoost);
        return similarity * boost;
    }

    /**
     * Analyze color distribution in the base image
     */
    analyzeColorDistribution(imageData, width, height) {
        const distribution = new Map();
        let totalPixels = 0;

        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const pixel = this.getPixelRGB(imageData, x, y, width);
                const colorKey = this.getColorKey(pixel);

                distribution.set(colorKey, (distribution.get(colorKey) || 0) + 1);
                totalPixels++;
            }
        }

        distribution.set('_total', totalPixels);
        return distribution;
    }

    /**
     * Calculate edge map using Sobel operator
     */
    calculateEdgeMap(imageData, width, height) {
        const edgeMap = [];

        // Sobel kernels
        const sobelX = [[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]];
        const sobelY = [[-1, -2, -1], [0, 0, 0], [1, 2, 1]];

        for (let y = 0; y < height; y++) {
            const row = [];
            for (let x = 0; x < width; x++) {
                let gx = 0, gy = 0;

                // Apply Sobel kernels
                for (let ky = -1; ky <= 1; ky++) {
                    for (let kx = -1; kx <= 1; kx++) {
                        const px = Math.max(0, Math.min(width - 1, x + kx));
                        const py = Math.max(0, Math.min(height - 1, y + ky));

                        const pixel = this.getPixelRGB(imageData, px, py, width);
                        const intensity = (pixel.r + pixel.g + pixel.b) / 3;

                        gx += intensity * sobelX[ky + 1][kx + 1];
                        gy += intensity * sobelY[ky + 1][kx + 1];
                    }
                }

                // Calculate edge magnitude and normalize
                const magnitude = Math.sqrt(gx * gx + gy * gy) / 255;
                row.push(Math.max(0, Math.min(1, magnitude)));
            }
            edgeMap.push(row);
        }

        return edgeMap;
    }

    /**
     * Generate a color key for distribution analysis
     */
    getColorKey(pixel) {
        // Quantize colors to reduce key space (group similar colors)
        const quantize = (value) => Math.floor(value / 32) * 32;
        return `${quantize(pixel.r)},${quantize(pixel.g)},${quantize(pixel.b)}`;
    }
}